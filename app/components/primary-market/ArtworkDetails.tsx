import { PaintingData } from "../../types/market";
import { TYPOGRAPHY } from "../../constants/market";
import { getLocalizedText, getPaintingType, formatLifeDates } from "../../utils/market";

interface ArtworkDetailsProps {
  painting: PaintingData;
  lang: string;
}

export const ArtworkDetails = ({ painting, lang }: ArtworkDetailsProps) => {
  return (
    <div
      className="text-center text-gray-800"
      style={{
        width: TYPOGRAPHY.artworkDetailsContainer.width,
        opacity: 1
      }}
    >
      {/* Artist Name and Life Dates */}
      {painting.artist?.name && (
        <div
          className="font-karla font-bold text-center mb-1"
          style={{
            width: TYPOGRAPHY.artworkDetailsTitle.width,
            height: TYPOGRAPHY.artworkDetailsTitle.height,
            fontSize: TYPOGRAPHY.artworkDetailsTitle.fontSize,
            fontWeight: TYPOGRAPHY.artworkDetailsTitle.fontWeight,
            lineHeight: TYPOGRAPHY.artworkDetailsTitle.lineHeight,
            letterSpacing: '0%',
            textAlign: 'center',
            opacity: 1
          }}
        >
          {painting.artist.name}
          {painting.artist.life_dates?.date_of_birth && painting.artist.life_dates?.date_of_death && (
            <span
              className="font-karla font-normal"
              style={{
                fontSize: TYPOGRAPHY.artworkDetailsLifeDates.fontSize,
                fontWeight: TYPOGRAPHY.artworkDetailsLifeDates.fontWeight,
                lineHeight: TYPOGRAPHY.artworkDetailsLifeDates.lineHeight,
                letterSpacing: '0%',
                textAlign: 'center'
              }}
            >
              {' '}{formatLifeDates(painting.artist.life_dates.date_of_birth, painting.artist.life_dates.date_of_death)}
            </span>
          )}
        </div>
      )}

      {/* Artwork Information */}
      <div
        className="font-karla text-center"
        style={{
          width: TYPOGRAPHY.artworkDetailsInfo.width,
          height: TYPOGRAPHY.artworkDetailsInfo.height,
          margin: '0 auto',
          fontSize: TYPOGRAPHY.artworkDetailsInfo.fontSize,
          lineHeight: TYPOGRAPHY.artworkDetailsInfo.lineHeight,
          letterSpacing: '0%',
          opacity: 1
        }}
      >
        {/* Title and Year */}
        <div
          className="font-bold mb-1"
          style={{
            fontWeight: 700,
            fontSize: '12px',
            lineHeight: '18px'
          }}
        >
          {getLocalizedText(painting.title, lang)}{painting.year && `, ${painting.year}`}
        </div>

        {/* Painting Type */}
        {(painting.painting_type_fr || painting.painting_type_en) && (
          <div
            className="font-normal mb-1"
            style={{
              fontWeight: 400,
              fontSize: '12px',
              lineHeight: '18px'
            }}
          >
            {getPaintingType(painting, lang)}
          </div>
        )}

        {/* Dimensions */}
        {painting.dimension && (
          <div
            className="font-normal"
            style={{
              fontWeight: 400,
              fontSize: '12px',
              lineHeight: '18px'
            }}
          >
            {painting.dimension.width} x {painting.dimension.height} cm
          </div>
        )}
      </div>
    </div>
  );
};
