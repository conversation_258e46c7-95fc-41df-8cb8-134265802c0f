import Image from "next/image";
import { Artist } from "../../types/market";
import { TYPOGRAPHY, IMAGE_SIZES } from "../../constants/market";
import { formatLifeDates, buildArtistImageUrl } from "../../utils/market";

interface ArtistInfoProps {
  artist: Artist;
  year?: string;
}

export const ArtistInfo = ({ artist, year }: ArtistInfoProps) => {
  return (
    <div className="flex items-center space-x-4">
      {/* Artist Avatar */}
      {artist.image && (
        <div className="flex-shrink-0">
          <Image
            src={buildArtistImageUrl(artist.image)}
            alt={artist.name}
            width={IMAGE_SIZES.artist.width}
            height={IMAGE_SIZES.artist.height}
            className="rounded-full object-cover"
            onError={() => {
              console.error('Failed to load artist image:', buildArtistImageUrl(artist.image));
            }}
          />
        </div>
      )}

      {/* Artist Details */}
      <div className="flex-1">
        {/* Artist Name */}
        <h3
          className="font-karla font-bold"
          style={{
            color: 'var(--color-primary)',
            width: TYPOGRAPHY.artistName.width,
            height: TYPOGRAPHY.artistName.height,
            fontSize: TYPOGRAPHY.artistName.fontSize,
            fontWeight: TYPOGRAPHY.artistName.fontWeight,
            lineHeight: TYPOGRAPHY.artistName.lineHeight,
            letterSpacing: '0%',
            textAlign: 'justify',
            opacity: 1
          }}
        >
          {artist.name}
        </h3>

        {/* Life Dates or Year */}
        {artist.life_dates?.date_of_birth && artist.life_dates?.date_of_death ? (
          <p
            className="font-karla font-normal text-gray-600"
            style={{
              width: TYPOGRAPHY.artistDates.width,
              height: TYPOGRAPHY.artistDates.height,
              fontSize: TYPOGRAPHY.artistDates.fontSize,
              fontWeight: TYPOGRAPHY.artistDates.fontWeight,
              lineHeight: TYPOGRAPHY.artistDates.lineHeight,
              letterSpacing: '0%',
              textAlign: 'justify',
              opacity: 1
            }}
          >
            {formatLifeDates(artist.life_dates.date_of_birth, artist.life_dates.date_of_death)}
          </p>
        ) : year && (
          <p
            className="font-karla font-normal text-gray-600"
            style={{
              width: TYPOGRAPHY.artistDates.width,
              height: TYPOGRAPHY.artistDates.height,
              fontSize: TYPOGRAPHY.artistDates.fontSize,
              fontWeight: TYPOGRAPHY.artistDates.fontWeight,
              lineHeight: TYPOGRAPHY.artistDates.lineHeight,
              letterSpacing: '0%',
              textAlign: 'justify',
              opacity: 1
            }}
          >
            {year}
          </p>
        )}
      </div>
    </div>
  );
};
