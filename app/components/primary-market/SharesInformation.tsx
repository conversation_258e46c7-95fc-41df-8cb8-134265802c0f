import React from 'react';
import { PaintingData } from "../../types/market";
import { Dictionary } from "../../lib/get-translation";
import { TYPOGRAPHY } from "../../constants/market";
import { calculateAvailableShares, calculateSharePercentage } from "../../utils/market";

interface SharesInformationProps {
  painting: PaintingData;
  dictionary: Dictionary;
  lang: string;
}

export const SharesInformation = ({ painting, dictionary, lang }: SharesInformationProps) => {
  const availableShares = calculateAvailableShares(painting.total_part, painting.sold_out_part);
  const sharePercentage = calculateSharePercentage(availableShares, painting.total_part);
  const soldPercentage = 100 - sharePercentage;

  return (
    <div className="">
      <div className="mb-4">
        <div className="flex flex-wrap items-center gap-2 mb-4">
          <span
            className="font-karla"
            style={{
              fontSize: TYPOGRAPHY.sharesAvailableText.fontSize,
              fontWeight: TYPOGRAPHY.sharesAvailableText.fontWeight,
              lineHeight: TYPOGRAPHY.sharesAvailableText.lineHeight,
              color: TYPOGRAPHY.sharesAvailableText.color,
            }}
          >
            {lang === 'en' ? 'Available Shares:' : 'Parts Disponibles:'}
          </span>
          <span
            className="font-karla"
            style={{
              fontSize: TYPOGRAPHY.sharesAvailableNumber.fontSize,
              fontWeight: TYPOGRAPHY.sharesAvailableNumber.fontWeight,
              lineHeight: TYPOGRAPHY.sharesAvailableNumber.lineHeight,
              color: TYPOGRAPHY.sharesAvailableNumber.color,
            }}
          >
            {availableShares}
          </span>
          <span
            className="font-karla"
            style={{
              fontSize: TYPOGRAPHY.sharesAvailableText.fontSize,
              fontWeight: 700,
              lineHeight: TYPOGRAPHY.sharesAvailableText.lineHeight,
              color: TYPOGRAPHY.sharesAvailableText.color,
            }}
          >
            / {painting.total_part} {lang === 'en' ? 'Parts' : 'Parts'}
          </span>
        </div>

        {/* Progress Bar */}
        <div
          className="w-full bg-gray-200 mb-3 flex overflow-hidden"
          style={{
            height: TYPOGRAPHY.sharesProgressBar.height,
            borderRadius: TYPOGRAPHY.sharesProgressBar.borderRadius,
          }}
        >
          <div
            className="h-full bg-black flex items-center justify-center"
            style={{
              width: `${soldPercentage}%`
            }}
          >
            {soldPercentage > 15 && (
              <span
                className="font-karla"
                style={{
                  fontSize: TYPOGRAPHY.sharesProgressText.fontSize,
                  fontWeight: TYPOGRAPHY.sharesProgressText.fontWeight,
                  lineHeight: TYPOGRAPHY.sharesProgressText.lineHeight,
                  color: TYPOGRAPHY.sharesProgressText.color,
                }}
              >
                {soldPercentage.toFixed(0)}%
              </span>
            )}
          </div>
          <div
            className="h-full flex items-center justify-center"
            style={{
              width: `${sharePercentage}%`,
              backgroundColor: '#B8860B'
            }}
          >
            {sharePercentage > 15 && (
              <span
                className="font-karla"
                style={{
                  fontSize: TYPOGRAPHY.sharesProgressText.fontSize,
                  fontWeight: TYPOGRAPHY.sharesProgressText.fontWeight,
                  lineHeight: TYPOGRAPHY.sharesProgressText.lineHeight,
                  color: TYPOGRAPHY.sharesProgressText.color,
                }}
              >
                {sharePercentage.toFixed(2)}%
              </span>
            )}
          </div>
        </div>
        {/* Investors and Countries - Using placeholder values until API fields are clarified */}
        <div className="mt-6">
          <span
            className="font-karla"
            style={{
              fontSize: TYPOGRAPHY.sharesInvestorsText.fontSize,
              fontWeight: TYPOGRAPHY.sharesInvestorsText.fontWeight,
              lineHeight: TYPOGRAPHY.sharesInvestorsText.lineHeight,
              color: TYPOGRAPHY.sharesInvestorsText.color,
            }}
          >
            3621 {lang === 'en' ? 'Investors from' : 'Acquéreurs issus de'} 23 {lang === 'en' ? 'Countries' : 'Pays'}
          </span>
        </div>
      </div>
    </div>
  );
};