import React from 'react';
import { PaintingData } from "../../types/market";
import { TYPOGRAPHY } from "../../constants/market";
import { ArtistChart } from "./ArtistChart";

interface ArtistIndexAndShareProps {
  painting: PaintingData;
  lang: string;
}

export const ArtistIndexAndShare: React.FC<ArtistIndexAndShareProps> = ({ painting, lang }) => {
  return (
    <div className="w-full bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-20">
        {/* Section Title */}
        <div className="text-center mb-12">
          <h2
            className="font-prata mb-4"
            style={{
              fontSize: TYPOGRAPHY.artistIndexTitle.fontSize,
              fontWeight: TYPOGRAPHY.artistIndexTitle.fontWeight,
              lineHeight: TYPOGRAPHY.artistIndexTitle.lineHeight,
              color: TYPOGRAPHY.artistIndexTitle.color,
            }}
          >
            {lang === 'en' ? 'Artist Rating Index & Artist Share Rating' : 'Indice Cotation Artiste & Cote Part Artiste'}
          </h2>
        </div>

        {/* Chart Section */}
        <div className="">
          <ArtistChart painting={painting} />
        </div>
      </div>
    </div>
  );
};
