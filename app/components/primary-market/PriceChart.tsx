import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts";
import { EvolutionItem } from "../../types/market";

interface PriceChartProps {
  evolutionList: EvolutionItem[];
}

export const PriceChart = React.memo(({ evolutionList }: PriceChartProps) => {
  const chartData = React.useMemo(() =>
    evolutionList.map(item => ({
      year: item._id,
      artworkPrice: item.totalValue, // black line
      artistPrice: item.artistValue, // gold line
    })),
    [evolutionList]
  );

  return (
    <div className="">
      <div className="h-64 w-full" style={{ pointerEvents: 'none' }}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={chartData}
            style={{ outline: 'none' }}
          >
            {/* Removed CartesianGrid for cleaner look */}

            <XAxis
              dataKey="year"
              stroke="#6b7280"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />

            <YAxis
              stroke="#6b7280"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              domain={[0, 600]}
              ticks={[0, 150, 300, 450, 600]}
            />

            {/* Black Line - Artwork Price */}
            <Line
              type="monotone"
              dataKey="artworkPrice"
              stroke="#1f2937"
              strokeWidth={2}
              dot={false}
              activeDot={false}
              style={{ outline: 'none' }}
            />

            {/* Gold Line - Artist Price */}
            <Line
              type="monotone"
              dataKey="artistPrice"
              stroke="#b38642"
              strokeWidth={2}
              dot={false}
              activeDot={false}
              style={{ outline: 'none' }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
});
