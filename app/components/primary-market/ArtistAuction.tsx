import React, { useState } from 'react';
import Image from 'next/image';
import { PaintingData } from "../../types/market";
import { TYPOGRAPHY } from "../../constants/market";
import { buildImageUrl } from "../../utils/market";
import { FiEye, FiX } from 'react-icons/fi';

interface ArtistAuctionProps {
  painting: PaintingData;
  lang: string;
}

interface CompTableItem {
  img: string;
  painting_en: string;
  painting_fr: string;
  year: string;
  all_time_record_usd: string;
  all_time_record_euro: string;
  height: string;
  width: string;
}

export const ArtistAuction: React.FC<ArtistAuctionProps> = ({ painting, lang }) => {
  // Get comp_tables data and limit to 8 items
  const compTables = (painting.comp_tables || []).slice(0, 8) as CompTableItem[];

  // Modal state
  const [selectedImage, setSelectedImage] = useState<CompTableItem | null>(null);

  const openModal = (item: CompTableItem) => {
    setSelectedImage(item);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  // Extract just the title from the full painting description
  const extractTitle = (fullText: string): string => {
    // Split by " / " and take the first part (the title)
    return fullText.split(' / ')[0] || fullText;
  };

  return (
    <div className="w-full bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-20">
        {/* Section Title */}
        <div className="text-center mb-12">
          <h2
            className="font-prata"
            style={{
              fontSize: TYPOGRAPHY.artistIndexTitle.fontSize,
              fontWeight: TYPOGRAPHY.artistIndexTitle.fontWeight,
              lineHeight: TYPOGRAPHY.artistIndexTitle.lineHeight,
              color: TYPOGRAPHY.artistIndexTitle.color,
            }}
          >
            {lang === 'en' ? 'Artist Auction Records' : 'Records d\'Enchères de l\'Artiste'}
          </h2>
        </div>

        {/* Cards Grid - 4 per row */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {compTables.map((item, index) => (
            <div key={index} className="bg-white rounded-md border border-gray-200 shadow-sm hover:shadow-md transition-shadow overflow-hidden">
              {/* Image */}
              <div
                className="relative w-full h-[260px] bg-white p-4 flex items-center justify-center cursor-pointer group"
                onClick={() => openModal(item)}
              >
                <Image
                  src={buildImageUrl(process.env.NEXT_PUBLIC_BACKEND_BASE_URL || '', item.img)}
                  alt={lang === 'en' ? item.painting_en : item.painting_fr}
                  fill
                  className="object-contain transition-all duration-300 group-hover:brightness-50"
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw"
                />

                {/* Hover Overlay */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
                  <div className="flex flex-col items-center text-white">
                    <FiEye size={32} className="mb-2" />
                    <span className="text-sm font-medium">
                      {lang === 'en' ? 'Preview' : 'Aperçu'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Text */}
              <div className="px-4 py-3 space-y-1">
                {/* Title + Year */}
                <div className="flex justify-between items-start">
                  <h3 className="font-karla text-sm font-bold text-gray-900 leading-snug">
                    {extractTitle(lang === 'en' ? item.painting_en : item.painting_fr)}
                  </h3>
                  <span className="text-sm text-gray-500">{item.year}</span>
                </div>

                {/* Material */}
                <div className="text-xs text-gray-500 leading-snug">
                  Crayons de cire colorés sur papier
                </div>

                {/* Dimensions */}
                <div className="text-xs text-gray-500">
                  {item.height} × {item.width} cm
                </div>

                {/* Auction house + price */}
                <div className="flex justify-between items-center pt-2 border-t border-gray-100 mt-2">
                  <div className="text-xs text-gray-500">Christie's {item.year}</div>
                  <div className="text-base font-semibold text-gray-900">
                    ${parseInt(item.all_time_record_usd).toLocaleString()}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Show message if less than 8 items */}
        {compTables.length < 8 && (
          <div className="text-center mt-8">
            <p
              className="font-karla"
              style={{
                fontSize: '14px',
                fontWeight: 400,
                lineHeight: '20px',
                color: '#6B7280',
              }}
            >
              {lang === 'en'
                ? `Showing ${compTables.length} auction records`
                : `Affichage de ${compTables.length} records d'enchères`}
            </p>
          </div>
        )}
      </div>

      {/* Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 flex items-center justify-center z-50 p-4"
          style={{ backgroundColor: 'rgba(0, 0, 0, 0.15)' }}
          onClick={closeModal}
        >
          {/* Close Button - Fixed position */}
          <button
            onClick={closeModal}
            className="fixed top-8 right-8 z-60 text-gray-600 hover:text-gray-800 transition-all duration-200 p-2 bg-white rounded-full shadow-lg hover:scale-110 active:scale-95"
            aria-label="Close modal"
          >
            <FiX size={24} />
          </button>

          {/* Modal Image */}
          <div
            className="relative max-w-2xl max-h-[45vh] w-full h-full flex items-center justify-center"
            onClick={(e) => e.stopPropagation()}
          >
            <Image
              src={buildImageUrl(process.env.NEXT_PUBLIC_BACKEND_BASE_URL || '', selectedImage.img)}
              alt={lang === 'en' ? selectedImage.painting_en : selectedImage.painting_fr}
              fill
              className="object-contain"
              sizes="50vw"
              priority
            />
          </div>
        </div>
      )}
    </div>
  );
};
