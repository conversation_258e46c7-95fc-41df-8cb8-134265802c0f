"use client";

import { Artist } from "../../../types/artist";
import Image from "next/image";
import React, { useState, useEffect } from "react";
import {
  LineChart,
  Line,
  ResponsiveContainer,
  XAxis,
  YAxis,
  Tooltip,
  CartesianGrid,
} from "recharts";
import { FiInfo } from "react-icons/fi";

interface ArtistsShareRatingProps {
  artists: Artist[];
  dictionary: {
    title: string;
    cpa: string;
    paintings: string;
    description?: string;
    details?: string;
  };
}

const ArtistsShareRating: React.FC<ArtistsShareRatingProps> = ({
  artists,
  dictionary,
}) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // Artist colors to match the design
  const artistColors: Record<string, string> = {
    "Jean-<PERSON> Ba<PERSON>at": "var(--artist-basquiat)",
    "<PERSON> Soulages": "var(--artist-soulages)",
    "<PERSON> Warhol": "var(--artist-warhol)",
    "Pablo PICASSO": "var(--artist-picasso)",
    "Zao Wou-ki": "var(--artist-zao)",
    "David Hockney": "var(--artist-hockney)",
  };

  // Fallback colors for any additional artists
  const fallbackColors = [
    "var(--fallback-1)",
    "var(--fallback-2)",
    "var(--fallback-3)",
    "var(--fallback-4)",
    "var(--fallback-5)",
    "var(--fallback-6)",
    "var(--fallback-7)",
  ];

  const getColorForArtist = (artistName: string, index: number) => {
    return (
      artistColors[artistName] || fallbackColors[index % fallbackColors.length]
    );
  };

  const description = dictionary.description ||
    "La Cote Part (CP) des Artistes correspond à la valeur dans le temps d'une part des œuvres d'un même artiste, avec pour étalon de référence le centimètre carré";

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: string) => {
    if (!value) return "";
    const numValue = parseFloat(value);
    return `${numValue > 0 ? '+' : ''}${numValue}%`;
  };

  return (
    <div className="bg-white py-8 w-screen relative left-1/2 right-1/2 -ml-[50vw] -mr-[50vw]">
      <div className="max-w-[1400px] mx-auto lg:px-8">

        <div className={`text-center ${isMobile ? 'mb-4 mt-4' : 'mb-6'}`}>
          <h1 className={`font-prata text-black ${isMobile ? 'text-3xl mb-2' : 'mb-3'}`} style={isMobile ? {} : { fontSize: '45px' }}>
            {dictionary.title}
            <FiInfo className={`inline-block ml-2 text-black ${isMobile ? 'w-3 h-3' : 'w-4 h-4'}`} style={{ verticalAlign: 'super' }} />
          </h1>
          <p className={`text-black mx-auto leading-relaxed font-karla ${isMobile ? 'max-w-full text-sm px-1' : 'max-w-4xl'}`} style={isMobile ? {} : { fontSize: '20px' }}>
            {description}
          </p>
        </div>

        <div
          className={`grid justify-center ${isMobile ? 'grid-cols-1 gap-6' : 'grid-cols-3 gap-5'} relative`}
          style={!isMobile ? { gridTemplateColumns: 'repeat(3, calc(33.333% + 20px))' } : {}}
        >
          {artists.map((artist, index) => {
            const cpaValue = artist.ic_artist_usd;
            const percentChange = artist.ic_percentage || "0";
            const isPositive = artist.ic_direction === "up";
            const artistColor = getColorForArtist(artist.name, index);

            const chartData = artist.index_artist
              .map((item) => ({
                year: item.year,
                price: item.price.find((p) => p.curr === "USD")?.value || 0,
              }))
              .sort((a, b) => parseInt(a.year) - parseInt(b.year));

            // Calculate min and max for Y axis to create proper chart scaling
            const prices = chartData.map(item => item.price);
            const minPrice = Math.min(...prices) * 0.8;
            const maxPrice = Math.max(...prices) * 1.2;

            // Format birth-death years
            const birthYear = artist.life_dates?.date_of_birth ?
              new Date(artist.life_dates.date_of_birth).getFullYear() : "";
            const deathYear = artist.life_dates?.date_of_death ?
              new Date(artist.life_dates.date_of_death).getFullYear() : "";
            const yearRange = birthYear ?
              `(${birthYear}${deathYear ? ` - ${deathYear}` : ""})` : "";

            return (
              <div key={artist._id} className={`relative  ${isMobile ? 'px-8 py-4' : 'px-6 py-8 w-full max-w-md'}`}>
                {/* Artist Header */}
                <div className={`flex items-center justify-between font-karla w-full ${isMobile ? 'mb-4' : 'mb-10'}`}>
                  <div className="flex items-center">
                    <div
                      className="w-16 h-16 rounded-full mr-4 flex items-center justify-center overflow-hidden border-5 relative"
                      style={{ backgroundColor: artistColor, borderColor: artistColor }}
                    >
                      {artist.image ? (
                        <Image
                          src={`${process.env.NEXT_PUBLIC_BACKEND_BASE_URL}${artist.image}`}
                          alt={artist.name}
                          fill
                          className="rounded-full object-cover"
                        />
                      ) : (
                        <span className="text-white text-2xl font-bold">
                          {artist.name.charAt(0)}
                        </span>
                      )}
                    </div>
                    <div className="flex-1">
                      <h3
                        className="text-xl font-medium font-karla leading-tight"
                        style={{ color: artistColor }}
                      >
                        {artist.name}
                      </h3>
                      {yearRange && (
                        <span className="text-sm text-gray-500 block mt-1">{yearRange}</span>
                      )}
                    </div>
                  </div>
                  <div className="text-right flex-shrink-0 ml-4">
                    <div className="flex items-center justify-end mb-1">
                      <span className="text-sm text-black mr-2 whitespace-nowrap">CP Artiste</span>
                      <span className={`text-sm font-medium whitespace-nowrap ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
                        <span className="mr-1">{isPositive ? '▲' : '▼'}</span>
                        {formatPercentage(typeof percentChange === 'string' ? percentChange : String(percentChange))}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 whitespace-nowrap">
                      {formatCurrency(typeof cpaValue === 'string' ? parseFloat(cpaValue) || 0 : cpaValue || 0)} /Part
                    </p>
                  </div>
                </div>

                {/* Artist Data */}
                <div className="space-y-6">
                  {/* Divider line */}
                  <div className="border-t border-gray-200 my-1"></div>

                  {/* Chart Container */}
                  <div className={`${isMobile ? 'h-48 mb-4 -mx-4' : 'h-64 mb-6 -mx-8'}`}>
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={chartData}
                        margin={isMobile ? { top: 10, right: 5, bottom: 15, left: 5 } : { top: 15, right: 10, bottom: 20, left: 10 }}
                      >
                        <CartesianGrid
                          strokeDasharray="3 3"
                          stroke="#f0f0f0"
                          vertical={false}
                        />
                        <XAxis
                          dataKey="year"
                          tick={{ fontSize: isMobile ? 8 : 10, fill: '#666' }}
                          axisLine={{ stroke: '#e0e0e0' }}
                          tickLine={{ stroke: '#e0e0e0' }}
                          interval={2}
                          angle={0}
                          textAnchor="middle"
                        />
                        <YAxis
                          domain={[0, 240]}
                          ticks={[0, 80, 160, 240]}
                          tick={{ fontSize: isMobile ? 8 : 10, fill: '#666' }}
                          axisLine={false}
                          tickLine={false}
                          width={isMobile ? 35 : 50}
                        />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: 'white',
                            border: '1px solid #e0e0e0',
                            borderRadius: '6px',
                            fontSize: isMobile ? '10px' : '12px',
                            boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                            padding: isMobile ? '6px' : '12px',
                          }}
                          formatter={(value) => [formatCurrency(Number(value)), '']}
                        />
                        <Line
                          type="monotone"
                          dataKey="price"
                          stroke={artistColor}
                          strokeWidth={isMobile ? 2 : 2.5}
                          dot={false}
                          activeDot={{ r: isMobile ? 3 : 4, strokeWidth: 0 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>

                  {/* Divider line */}
                  <div className="border-t border-gray-200 my-1"></div>

                  {/* Paintings Grid */}
                  <div className="grid grid-cols-4 gap-1 mb-4">
                    {artist.comp_tables && artist.comp_tables.length > 0 ? (
                      artist.comp_tables.slice(0, 4).map((painting, paintingIndex) => (
                        <div key={paintingIndex} className="aspect-square relative overflow-hidden">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_BACKEND_BASE_URL}${painting.img}`}
                            alt={painting.painting_en || painting.painting_fr || `Artwork ${paintingIndex + 1}`}
                            fill
                            sizes="(max-width: 768px) 25vw, 20vw"
                            className="object-cover"
                          />
                        </div>
                      ))
                    ) : (
                      // Placeholder for missing paintings
                      Array.from({ length: 4 }).map((_, paintingIndex) => (
                        <div key={paintingIndex} className="aspect-square bg-gray-100 flex items-center justify-center">
                          <span className="text-gray-400 text-xs">No Image</span>
                        </div>
                      ))
                    )}
                  </div>

                  {/* Details Button */}
                  <div>
                    <button className="w-full py-2 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                      {dictionary.details || "DÉTAILS"}
                    </button>
                  </div>

                  {/* Vertical Dividers - Hidden on mobile */}
                  {!isMobile && (index + 1) % 3 !== 0 && index !== artists.length - 1 && (
                    <div className="absolute -right-6 top-0 bottom-0 w-px bg-gray-300"></div>
                  )}

                  {/* Horizontal Dividers - Hidden on mobile */}
                  {!isMobile && index < artists.length - 3 && (
                    <div className="absolute left-0 -right-3 bottom-0 h-px bg-gray-300"></div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default ArtistsShareRating;
