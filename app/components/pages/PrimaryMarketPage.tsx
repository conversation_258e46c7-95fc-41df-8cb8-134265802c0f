import { getDictionary } from "../../lib/get-translation";
import { ApiResponse } from "../../types/market";
import PrimaryMarketClient from "./PrimaryMarketClient";

async function getPrimaryArtworkData(): Promise<ApiResponse> {
  try {
    const apiUrl = "https://api.weart.exchange/paintings/?type=primary&limit=1&offset=0&currency=USD";
    console.log('Fetching from URL:', apiUrl);

    const response = await fetch(apiUrl, {
      next: { revalidate: 3600 },
      cache: 'no-store' // Temporarily disable cache for debugging
    });

    console.log('Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error Response:', errorText);
      throw new Error(`Failed to fetch primary artwork data: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('API Response data:', data);
    return data;
  } catch (error) {
    console.error("Error fetching primary artwork data:", error);
    return { paintings: [], base_url: "" };
  }
}

export default async function PrimaryMarketPage({ lang }: { lang: "en" | "fr" }) {
  const [dictionary, artworkData] = await Promise.all([
    getDictionary(lang),
    getPrimaryArtworkData(),
  ]);

  return (
    <PrimaryMarketClient
      dictionary={dictionary}
      artworkData={artworkData}
      lang={lang}
    />
  );
}
