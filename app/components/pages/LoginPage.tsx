
"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Dictionary } from "../../lib/get-translation";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import { z } from 'zod'; // Import zod
import Button from "./variants/button";
import { setAuthData } from "../../lib/auth";

interface LoginPageProps {
  dictionary: Dictionary;
  lang: string;
}

// Define validation schema using dictionary for messages
const getLoginSchema = (dictionary: Dictionary) => z.object({
  email: z.string().email({ message: dictionary.auth.login.validation?.invalid_email || "Invalid email format" }),
  password: z.string().min(8, dictionary.auth.login.validation?.password_length || "Password must be at least 8 characters")
});

export default function LoginPage({ dictionary, lang }: LoginPageProps) {
  const router = useRouter();
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [apiError, setApiError] = useState("");
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);

  const loginSchema = getLoginSchema(dictionary);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setApiError("");
    setFormErrors({});

    const result = loginSchema.safeParse(formData);

    if (!result.success) {
      const errors: Record<string, string> = {};
      result.error.issues.forEach(issue => {
        const fieldName = issue.path[0] as string;
        errors[fieldName] = issue.message;
      });
      setFormErrors(errors);
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/token`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password,
          device_token: "web-app",
          device_type: "web",
        }),
      });

      if (!response.ok) {
        throw new Error(dictionary.auth.login.invalid_credentials);
      }

      const data = await response.json();

      // Use the new auth function
      setAuthData(data.token, data.user, data.refresh_token);

      const dashboardPath = lang === "fr" ? "/fr/tableau-de-bord" : "/dashboard";
      router.push(dashboardPath);
    } catch (err) {
      setApiError(err instanceof Error ? err.message : dictionary.auth.login.error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    // Clear errors on change
    if (formErrors[e.target.name]) {
      setFormErrors({
        ...formErrors,
        [e.target.name]: "",
      });
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Container with Figma grid specifications */}
      <div className="mx-4 lg:mx-[120px] py-8 lg:py-16 flex justify-center">
        {/* Main Content Wrapper */}
        <div className="w-full max-w-7xl">
          <div className="mt-8 lg:mt-5 mb-8 lg:mb-9 -ml-2">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl text-black text-center lg:text-left font-prata mb-6 lg:mb-12">
              {dictionary.auth.login.title}
            </h1>
            <div className="grid grid-cols-1 lg:grid-cols-12 lg:gap-x-20 items-start">
              {/* Left Side - Form */}
              <div className="col-span-1 lg:col-span-6">
                <form onSubmit={handleSubmit} className="space-y-6 lg:space-y-8" noValidate>
                  {/* Email Field */}
                  <div>
                    <label htmlFor="email" className="block text-sm sm:text-base text-black mb-2 font-karla">
                      {dictionary.auth.login.email} <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full py-2 sm:py-3 px-0 text-sm sm:text-base text-black bg-transparent border-0 border-b border-gray-300 focus:border-black focus:outline-none focus:bg-transparent transition-colors font-karla"
                      style={{
                        backgroundColor: 'transparent !important',
                        boxShadow: 'none',
                        WebkitBoxShadow: 'none',
                        WebkitTextFillColor: 'black',
                        WebkitBackgroundClip: 'text'
                      }}
                      autoComplete="email"
                      required
                    />
                    {formErrors.email && (
                      <p className="text-red-500 text-xs sm:text-sm mt-1">{formErrors.email}</p>
                    )}
                  </div>

                  {/* Password Field */}
                  <div>
                    <label htmlFor="password" className="block text-sm sm:text-base text-black mb-2 font-karla">
                      {dictionary.auth.login.password} <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type={showPassword ? "text" : "password"}
                        id="password"
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        className="w-full py-2 sm:py-3 px-0 pr-10 text-sm sm:text-base text-black bg-transparent border-0 border-b border-gray-300 focus:border-black focus:outline-none focus:bg-transparent transition-colors font-karla"
                        style={{
                          backgroundColor: 'transparent !important',
                          boxShadow: 'none',
                          WebkitBoxShadow: 'none',
                          WebkitTextFillColor: 'black',
                          WebkitBackgroundClip: 'text'
                        }}
                        autoComplete="current-password"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-0 top-2 sm:top-4 text-gray-500 hover:text-black flex-shrink-0"
                      >
                        {showPassword ? (
                          <AiOutlineEyeInvisible className="h-4 w-4 sm:h-5 sm:w-5" />
                        ) : (
                          <AiOutlineEye className="h-4 w-4 sm:h-5 sm:w-5" />
                        )}
                      </button>
                    </div>
                    {formErrors.password && (
                      <p className="text-red-500 text-xs sm:text-sm mt-1">{formErrors.password}</p>
                    )}
                  </div>

                  {/* Remember Me & Forgot Password */}
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                    <div className="flex items-center">
                      <div className="relative flex items-center">
                        <input
                          id="remember-me"
                          name="remember-me"
                          type="checkbox"
                          className="sr-only"
                          checked={formData.rememberMe || false}
                          onChange={(e) => setFormData({ ...formData, rememberMe: e.target.checked })}
                        />
                        <div
                          className={`h-4 w-4  border-2 flex items-center justify-center cursor-pointer transition-all duration-200 ${formData.rememberMe
                            ? 'bg-[var(--color-primary)] border-[var(--color-primary)]'
                            : 'bg-white border-gray-300 hover:border-gray-400'
                            }`}
                          onClick={() => setFormData({ ...formData, rememberMe: !formData.rememberMe })}
                        >
                          {formData.rememberMe && (
                            <svg
                              className="h-3 w-3 text-white"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                          )}
                        </div>
                      </div>
                      <label
                        htmlFor="remember-me"
                        className="ml-2 text-sm sm:text-base text-black font-karla cursor-pointer"
                        onClick={() => setFormData({ ...formData, rememberMe: !formData.rememberMe })}
                      >
                        {dictionary.auth.login.remember_me}
                      </label>
                    </div>
                    <a href="#" className="text-sm sm:text-base text-[var(--color-primary)] hover:text-[var(--color-primary)] font-karla">
                      {dictionary.auth.login.forgot_password}
                    </a>
                  </div>

                  {/* API Error */}
                  {apiError && (
                    <div className="text-red-600 text-sm sm:text-base bg-red-50 p-3 rounded-md">
                      {apiError}
                    </div>
                  )}

                  {/* Submit Button */}
                  <Button
                    type="submit"
                    disabled={isLoading}
                    variant="auth"
                    width="w-full"
                  >
                    {isLoading ? dictionary.auth.login.submitting : dictionary.auth.login.submit}
                  </Button>

                  {/* Divider */}
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t border-gray-300"></div>
                    </div>
                    <div className="relative flex justify-center text-sm sm:text-base">
                      <span className="px-4 sm:px-6 bg-white text-black font-karla">
                        {dictionary.auth.login.or}
                      </span>
                    </div>
                  </div>

                  {/* Social Login Buttons */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 -mt-2">
                    <button
                      type="button"
                      className="flex items-center justify-center py-3 sm:py-4 px-4 border-2 border-gray-300 bg-white text-sm sm:text-base font-medium hover:bg-gray-50 hover:border-black transition-colors duration-300 ease-in-out"
                    >
                      <img
                        src="/google.svg"
                        alt="Google"
                        className="w-6 h-6 sm:w-7 sm:h-7 mr-2 sm:mr-3 flex-shrink-0 -mt-px"
                      />
                      <span className="font-karla font-bold truncate">{dictionary.auth.login.continue_with_google}</span>
                    </button>
                    <button
                      type="button"
                      className="flex items-center justify-center py-3 sm:py-4 px-4 border-2 border-gray-300 bg-white text-sm sm:text-base font-medium hover:bg-gray-50 hover:border-black transition-colors duration-300 ease-in-out"
                    >
                      <img
                        src="/microsoft.svg"
                        alt="Microsoft"
                        className="w-6 h-6 sm:w-7 sm:h-7 mr-2 sm:mr-3 flex-shrink-0 -mt-px"
                      />
                      <span className="font-karla font-bold truncate">{dictionary.auth.login.continue_with_microsoft}</span>
                    </button>
                  </div>

                  {/* Sign Up Link */}
                  <div className="text-center">
                    <p className="text-sm sm:text-base text-black font-karla">
                      {dictionary.auth.login.no_account}{' '}
                      <a
                        href="/register"
                        className="text-[var(--color-primary)] underline underline-offset-5  -mr-5 ml-2"
                      >
                        {dictionary.auth.login.sign_up}
                      </a>
                    </p>
                  </div>
                </form>
              </div>

              {/* Right Side - Image */}
              <div className="hidden lg:flex lg:col-span-6 justify-center">
                <div className="w-full h-full flex items-center justify-center">
                  <Image
                    src="/dollar.png"
                    alt={dictionary.auth.login.image_alt}
                    width={630}
                    height={810}
                    className="object-contain w-full h-full pb-5" // pb-5 is 20px
                    priority
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
