"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from 'next/image';
import { Dictionary } from "../../lib/get-translation";
import Button from "./variants/button";
import { PhoneInput } from "react-international-phone";
import "react-international-phone/style.css";
import { RegisterPageProps, RegisterFormData } from "../../types/login";
import { isValidPhoneNumber } from "libphonenumber-js";
import { countries } from "../../lib/countries";
import { z } from 'zod';
import EyeIcon from "../icons/EyeIcon";
import EyeClosedIcon from "../icons/EyeClosedIcon";

const getRegisterSchema = (dictionary: Dictionary) => z.object({
  first_name: z.string()
    .min(1, dictionary.auth.register.validation?.name_required || "Required field.")
    .max(100, dictionary.auth.register.validation?.name_max_length || "Cannot contain more than 100 characters.")
    .regex(/^[\p{L}\s'-]+$/u, { message: dictionary.auth.register.validation?.name_invalid_characters || "Cannot contain special characters or numbers." }),
  last_name: z.string()
    .min(1, dictionary.auth.register.validation?.last_name_required || "Required field.")
    .max(100, dictionary.auth.register.validation?.last_name_max_length || "Cannot contain more than 100 characters.")
    .regex(/^[\p{L}\s'-]+$/u, { message: dictionary.auth.register.validation?.last_name_invalid_characters || "Cannot contain special characters or numbers." }),
  email: z.string()
    .min(1, dictionary.auth.register.validation?.email_required || "Required field.")
    .max(100, dictionary.auth.register.validation?.email_max_length || "Invalid email.")
    .email({ message: dictionary.auth.register.validation?.invalid_email || "Invalid email." }),
  number: z.string().refine(isValidPhoneNumber, { message: dictionary.auth.register.validation?.phone_required || "Required field." }),
  password: z.string()
    .min(8, { message: (dictionary.auth.register.validation as any)?.password_complexity || "Must contain at least 8 characters including a special character." })
    .max(100, { message: (dictionary.auth.register.validation as any)?.password_max_length || "Cannot contain more than 100 characters." })
    .regex(/^(?=.*[^a-zA-Z0-9]).+$/, { message: (dictionary.auth.register.validation as any)?.password_complexity || "Must contain at least 8 characters including a special character." }),
  country: z.string().min(1, { message: dictionary.auth.register.validation?.country_required || "Required field." }),
  acceptTermsAndPrivacy: z.boolean().refine(val => val === true, {
    message: dictionary.auth.register.validation?.accept_terms || "You must accept the terms and privacy policy."
  })
});

export default function RegisterPage({ dictionary, lang }: RegisterPageProps) {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [isMounted, setIsMounted] = useState(false);
  const [touchedFields, setTouchedFields] = useState<Record<string, boolean>>({});
  const [isCountryDropdownOpen, setIsCountryDropdownOpen] = useState(false);

  const [formData, setFormData] = useState<RegisterFormData>({
    first_name: "",
    last_name: "",
    slug: "tn",
    country_code: "216",
    number: "",
    email: "",
    password: "",
    country: "",
    acceptTermsAndPrivacy: false,
  });

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted) return;

    const registerSchema = getRegisterSchema(dictionary);
    const result = registerSchema.safeParse(formData);

    if (!result.success) {
      const newErrors: Record<string, string> = {};
      result.error.issues.forEach((err) => {
        const path = err.path[0];
        if (typeof path === 'string') {
          newErrors[path] = err.message;
        }
      });
      setFormErrors(newErrors);
    } else {
      setFormErrors({});
    }
  }, [formData, dictionary, isMounted]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setTouchedFields({
      first_name: true,
      last_name: true,
      email: true,
      number: true,
      password: true,
      country: true,
      acceptTermsAndPrivacy: true
    });

    const registerSchema = getRegisterSchema(dictionary);
    const result = registerSchema.safeParse(formData);

    if (!result.success) {
      const newErrors: Record<string, string> = {};
      result.error.issues.forEach((err) => {
        const path = err.path[0];
        if (typeof path === 'string') {
          newErrors[path] = err.message;
        }
      });
      setFormErrors(newErrors);
      return;
    }

    setIsLoading(true);
    setApiError(null);
    setFormErrors({});

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(result.data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 409) {
          setApiError(dictionary.auth.register.validation?.email_conflict || "An account with this email already exists.");
        } else {
          setApiError(errorData.message || dictionary.auth.register.validation?.unknown_error || "An unknown error occurred.");
        }
        return;
      }

      router.push(`/${lang}/auth/login`);
    } catch (error) {
      setApiError(dictionary.auth.register.validation?.network_error || "A network error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    const newValue = type === "checkbox" ? (e.target as HTMLInputElement).checked : value;

    setFormData(prev => ({
      ...prev,
      [name]: newValue,
    }));

    if (apiError) setApiError(null);

    if (!touchedFields[name]) {
      setTouchedFields(prev => ({ ...prev, [name]: true }));
    }
  };

  const handlePhoneChange = (phone: string) => {
    setFormData((prev) => ({ ...prev, number: phone }));
    if (!touchedFields.number) {
      setTouchedFields((prev) => ({ ...prev, number: true }));
    }
  };

  const allOtherFieldsFilled =
    formData.first_name !== "" &&
    formData.last_name !== "" &&
    formData.email !== "" &&
    formData.number !== "" &&
    formData.password !== "" &&
    formData.country !== "";

  if (!isMounted) {
    return null;
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Container with Figma grid specifications */}
      <div className="mx-4 lg:mx-[120px] py-8 lg:py-16 flex justify-center">
        {/* Main Content Wrapper */}
        <div className="w-full max-w-7xl">
          <div className="mt-8 lg:mt-5 mb-8 lg:mb-9 -ml-2">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl text-black text-center lg:text-left font-prata mb-6 lg:mb-12">
              {dictionary.auth.register.title}
            </h1>
            <div className="grid grid-cols-1 lg:grid-cols-12 lg:gap-x-20 items-start">
              {/* Left Side - Form */}
              <div className="col-span-1 lg:col-span-6 relative">
                <form onSubmit={handleSubmit} className="space-y-6 lg:space-y-8" noValidate>
                  <div className="grid grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm sm:text-base text-black mb-0.5 font-karla ">
                        {dictionary.auth.register.first_name} <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        name="first_name"
                        maxLength={100}
                        className="w-full py-1 px-0 text-sm sm:text-base text-black bg-transparent border-0 border-b border-gray-300 focus:border-black focus:outline-none focus:bg-transparent transition-colors font-karla"
                        style={{
                          backgroundColor: 'transparent !important',
                          boxShadow: 'none',
                          WebkitBoxShadow: 'none',
                          WebkitTextFillColor: 'black',
                          WebkitBackgroundClip: 'text'
                        }}
                        value={formData.first_name}
                        onChange={handleInputChange}
                      />
                      {touchedFields.first_name && formErrors.first_name && <p className="text-red-500 text-xs sm:text-sm mt-1 font-karla">{formErrors.first_name}</p>}
                    </div>
                    <div>
                      <label className="block text-sm sm:text-base text-black mb-0.5 font-karla">
                        {dictionary.auth.register.last_name} <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        name="last_name"
                        maxLength={100}
                        className="w-full py-1 px-0 text-sm sm:text-base text-black bg-transparent border-0 border-b border-gray-300 focus:border-black focus:outline-none focus:bg-transparent transition-colors font-karla"
                        style={{
                          backgroundColor: 'transparent !important',
                          boxShadow: 'none',
                          WebkitBoxShadow: 'none',
                          WebkitTextFillColor: 'black',
                          WebkitBackgroundClip: 'text'
                        }}
                        value={formData.last_name}
                        onChange={handleInputChange}
                      />
                      {touchedFields.last_name && formErrors.last_name && <p className="text-red-500 text-xs sm:text-sm mt-1 font-karla">{formErrors.last_name}</p>}
                    </div>
                  </div>

                  <div className="relative">
                    <label htmlFor="country" className="block text-sm sm:text-base font-medium text-gray-700 font-karla">
                      {dictionary.auth.register.country}
                    </label>
                    <div className="relative">
                      <select
                        id="country"
                        name="country"
                        value={formData.country}
                        onChange={handleInputChange}
                        onFocus={() => setIsCountryDropdownOpen(true)}
                        onBlur={() => setIsCountryDropdownOpen(false)}
                        onClick={() => setIsCountryDropdownOpen(!isCountryDropdownOpen)}
                        className="mt-1 block w-full pr-10 py-1 text-sm sm:text-base bg-transparent border-b-1 border-gray-300 focus:outline-none focus:ring-0 focus:border-black custom-select appearance-none"
                      >
                        <option value="" disabled>{dictionary.auth.register.select_country}</option>
                        {countries.map((c) => (
                          <option key={c.code} value={c.code}>
                            {c.name}
                          </option>
                        ))}
                      </select>
                      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-0">
                        <Image
                          src="/arrow-left.svg"
                          alt="dropdown arrow"
                          width={16}
                          height={16}
                          className={`w-3 h-3 transition-transform duration-200 ease-in-out ${isCountryDropdownOpen ? 'rotate-180' : ''}`}
                        />
                      </div>
                    </div>
                    {touchedFields.country && formErrors.country && <p className="text-red-500 text-xs sm:text-sm mt-1 font-karla">{formErrors.country}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      {dictionary.auth.register.phone} <span className="text-red-500">*</span>
                    </label>
                    <PhoneInput
                      className="custom-phone-input-container"
                      defaultCountry="tn"
                      value={formData.number}
                      onChange={handlePhoneChange}
                      inputClassName="w-full py-1 px-0 text-xl text-black bg-transparent border-0 focus:outline-none focus:bg-transparent transition-colors font-karla"
                      countrySelectorStyleProps={{
                        dropdownArrowClassName: "hidden",
                      }}
                    />
                    {touchedFields.number && formErrors.number && <p className="text-red-500 text-sm mt-1 font-karla">{formErrors.number}</p>}
                  </div>

                  <div>
                    <label className="block text-sm sm:text-base text-black mb-0.5 font-karla">
                      {dictionary.auth.register.email} <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="email"
                      name="email"
                      maxLength={40}
                      className="w-full py-1 px-0 text-sm sm:text-base text-black bg-transparent border-0 border-b border-gray-300 focus:border-black focus:outline-none focus:bg-transparent transition-colors font-karla"
                      style={{
                        backgroundColor: 'transparent !important',
                        boxShadow: 'none',
                        WebkitBoxShadow: 'none',
                        WebkitTextFillColor: 'black',
                        WebkitBackgroundClip: 'text'
                      }}
                      value={formData.email}
                      onChange={handleInputChange}
                    />
                    {touchedFields.email && formErrors.email && <p className="text-red-500 text-xs sm:text-sm mt-1 font-karla">{formErrors.email}</p>}
                  </div>

                  <div>
                    <label className="block text-sm sm:text-base text-black mb-0.5 font-karla">
                      {dictionary.auth.register.password} <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type={showPassword ? "text" : "password"}
                        name="password"
                        maxLength={40}
                        value={formData.password}
                        onChange={handleInputChange}
                        className="w-full py-1 px-0 pr-10 text-sm sm:text-base text-black bg-transparent border-0 border-b border-gray-300 focus:border-black focus:outline-none font-mono transition-colors"
                        style={{
                          fontFamily: '"Courier New", Courier, monospace',
                          backgroundColor: 'transparent',
                          boxShadow: 'none',
                          WebkitBoxShadow: 'none',
                          WebkitTextFillColor: 'black',
                          WebkitBackgroundClip: 'text',
                        }}
                      />

                      {/* Eye icon wrapper with fixed size */}
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-0 bottom-0 h-full w-10 flex items-end justify-end"
                        style={{ marginRight: '-8px' }}
                        tabIndex={-1}
                      >
                        <div className="w-7 h-9 flex items-center justify-center">
                          {showPassword ? (
                            <EyeClosedIcon className="w-9 h-11 text-black transition-all mr-1 duration-200 ease-in-out" />
                          ) : (
                            <EyeIcon className="w-7 h-7 text-black transition-all duration-200 ease-in-out" />
                          )}
                        </div>
                      </button>
                    </div>

                    {touchedFields.password && formErrors.password && <p className="text-red-500 text-xs sm:text-sm mt-1 font-karla">{formErrors.password}</p>}
                  </div>

                  <div className="mt-11 mb-5">
                    <Button
                      type="submit"
                      disabled={isLoading}
                      variant="auth"
                      width="w-full"
                    >
                      {isLoading
                        ? dictionary.auth.register.submitting
                        : dictionary.auth.register.submit.toUpperCase()
                      }
                    </Button>

                  </div>

                  <div className="space-y-5 pt-4">
                    <p className="text-black text-sm sm:text-base font-karla" style={{ wordSpacing: '0.5rem' }}>
                      {dictionary.auth.register.checkbox_disclaimer}
                    </p>


                    <div className="flex items-center space-x-2">
                      <div className="relative flex items-center">
                        <div
                          className={`h-4 w-4 border-2 flex items-center justify-center cursor-pointer transition-all duration-200 ${formData.acceptTermsAndPrivacy
                            ? 'bg-[var(--color-primary)] border-[var(--color-primary)]'
                            : 'bg-white border-gray-300 hover:border-gray-400'
                            }`}
                          onClick={() => {
                            setFormData({ ...formData, acceptTermsAndPrivacy: !formData.acceptTermsAndPrivacy });
                            if (!touchedFields.acceptTermsAndPrivacy) {
                              setTouchedFields(prev => ({ ...prev, acceptTermsAndPrivacy: true }));
                            }
                          }}
                        >

                          {formData.acceptTermsAndPrivacy && (
                            <svg
                              className="h-3 w-3 text-white"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                          )}
                        </div>
                      </div>

                      <label
                        htmlFor="acceptTermsAndPrivacy"
                        className="text-gray-700 text-sm sm:text-base font-karla cursor-pointer"
                        onClick={() => {
                          setFormData({ ...formData, acceptTermsAndPrivacy: !formData.acceptTermsAndPrivacy });
                          if (!touchedFields.acceptTermsAndPrivacy) {
                            setTouchedFields(prev => ({ ...prev, acceptTermsAndPrivacy: true }));
                          }
                        }}
                      >

                        {lang === "fr" ? "J'accepte " : "I accept "}
                        <a href="#" className="text-[var(--color-primary)] hover:underline">
                          {dictionary.auth.register.terms}
                          <span className="text-[var(--color-primary)]">{lang === "fr" ? " et la " : " and the "}</span>
                          {dictionary.auth.register.privacy}.
                        </a>
                      </label>

                      <input
                        id="acceptTermsAndPrivacy"
                        name="acceptTermsAndPrivacy"
                        type="checkbox"
                        className="sr-only"
                        checked={formData.acceptTermsAndPrivacy}
                        onChange={handleInputChange}
                      />
                    </div>
                    {(touchedFields.acceptTermsAndPrivacy || allOtherFieldsFilled) && formErrors.acceptTermsAndPrivacy && <p className="text-red-500 text-xs sm:text-sm mt-2 font-karla">{formErrors.acceptTermsAndPrivacy}</p>}
                  </div>

                  {apiError && (
                    <div className="text-red-600 text-sm sm:text-base bg-red-50 p-3 rounded-md font-karla">
                      {apiError}
                    </div>
                  )}


                </form>

                {/* Image positioned to the right of the form */}
                <div className="hidden lg:block absolute top-[0px] right-[-750px] xl:right-[-800px] 2xl:right-[-850px] w-[550px] xl:w-[650px] 2xl:w-[700px] h-[700px] xl:h-[800px] 2xl:h-[850px]">
                  <Image
                    src="/dollar.png"
                    alt={dictionary.auth.register.title || 'Registration page artwork'}
                    width={700}
                    height={850}
                    className="object-contain max-w-full h-auto"
                    priority
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  );
}
