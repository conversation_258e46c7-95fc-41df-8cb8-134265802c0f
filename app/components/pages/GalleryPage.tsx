import { getDictionary } from "../../lib/get-translation";
import PhotoGallery from "../PhotoGallery";

interface GalleryItem {
  _id: string;
  title: {
    en: string;
    fr: string;
  };
  img: string;
}

interface ApiResponse {
  galleries: GalleryItem[];
  base_url: string;
}

async function getGalleryData(): Promise<ApiResponse> {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/content/galleries`,
      { next: { revalidate: 3600 } }
    );
    if (!response.ok) {
      throw new Error('Failed to fetch gallery data');
    }
    return await response.json();
  } catch (error) {
    console.error("Error fetching gallery data:", error);
    return { galleries: [], base_url: "" };
  }
}
export default async function GalleryPage({ lang }: { lang: "en" | "fr" }) {
  const [dictionary, galleryData] = await Promise.all([
    getDictionary(lang),
    getGalleryData(),
  ]);

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {dictionary.nav.gallery}
          </h1>
        </div>

        <PhotoGallery
          galleries={galleryData.galleries}
          baseUrl={galleryData.base_url}
          lang={lang}
        />
      </div>
    </div>
  );
}
