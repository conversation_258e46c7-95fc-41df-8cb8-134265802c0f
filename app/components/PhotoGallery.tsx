"use client";

import Image from "next/image";
import { useState, useEffect, useCallback } from "react";
import { FiX, FiChevronLeft, FiChevronRight } from "react-icons/fi";

interface GalleryItem {
  _id: string;
  title: {
    en: string;
    fr: string;
  };
  img: string;
}

interface PhotoGalleryProps {
  galleries: GalleryItem[];
  baseUrl: string;
  lang: "en" | "fr";
}

export default function PhotoGallery({
  galleries,
  baseUrl,
  lang,
}: PhotoGalleryProps) {
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);

  const openFullscreen = (index: number) => {
    setSelectedIndex(index);
  };

  const closeFullscreen = () => {
    setSelectedIndex(null);
  };

  const goToPrevious = useCallback(() => {
    if (selectedIndex !== null && selectedIndex > 0) {
      setSelectedIndex(selectedIndex - 1);
    }
  }, [selectedIndex]);

  const goToNext = useCallback(() => {
    if (selectedIndex !== null && selectedIndex < galleries.length - 1) {
      setSelectedIndex(selectedIndex + 1);
    }
  }, [selectedIndex, galleries.length]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (selectedIndex === null) return;

      switch (event.key) {
        case "Escape":
          closeFullscreen();
          break;
        case "ArrowLeft":
          goToPrevious();
          break;
        case "ArrowRight":
          goToNext();
          break;
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [selectedIndex, goToPrevious, goToNext]);

  useEffect(() => {
    if (selectedIndex !== null) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.body.style.overflow = "unset";
    };
  }, [selectedIndex]);

  if (!galleries || galleries.length === 0) {
    return (
      <div className="flex items-center justify-center py-20">
        <p className="text-gray-500">No images available</p>
      </div>
    );
  }

  return (
    <>
      {/* Gallery Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
        {galleries.map((item, index) => (
          <div
            key={item._id}
            className="group relative aspect-square overflow-hidden cursor-pointer bg-gray-100"
            onClick={() => openFullscreen(index)}
          >
            <Image
              src={`${baseUrl}${item.img}`}
              alt={item.title[lang]}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
            <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <h3 className="text-white font-medium text-sm truncate">
                {item.title[lang]}
              </h3>
            </div>
          </div>
        ))}
      </div>

      {/* Fullscreen Modal */}
      {selectedIndex !== null && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-95 flex items-center justify-center">
          {/* Close Button */}
          <button
            onClick={closeFullscreen}
            className="absolute top-4 right-4 z-60 text-white hover:text-gray-300 transition-colors p-2"
            aria-label="Close fullscreen"
          >
            <FiX size={32} />
          </button>

          {/* Navigation Buttons */}
          {selectedIndex > 0 && (
            <button
              onClick={goToPrevious}
              className="absolute left-4 top-1/2 -translate-y-1/2 z-60 text-white hover:text-gray-300 transition-colors p-2"
              aria-label="Previous image"
            >
              <FiChevronLeft size={32} />
            </button>
          )}

          {selectedIndex < galleries.length - 1 && (
            <button
              onClick={goToNext}
              className="absolute right-4 top-1/2 -translate-y-1/2 z-60 text-white hover:text-gray-300 transition-colors p-2"
              aria-label="Next image"
            >
              <FiChevronRight size={32} />
            </button>
          )}

          {/* Main Image */}
          <div className="relative max-w-[90vw] max-h-[90vh] w-full h-full flex items-center justify-center">
            <Image
              src={`${baseUrl}${galleries[selectedIndex].img}`}
              alt={galleries[selectedIndex].title[lang]}
              fill
              className="object-contain"
              sizes="90vw"
              priority
            />
          </div>

          {/* Image Title */}
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-60 text-center">
            <h2 className="text-white text-lg font-medium mb-2">
              {galleries[selectedIndex].title[lang]}
            </h2>
            <p className="text-gray-300 text-sm">
              {selectedIndex + 1} / {galleries.length}
            </p>
          </div>

          {/* Click to close overlay */}
          <div
            className="absolute inset-0 cursor-pointer"
            onClick={closeFullscreen}
            aria-label="Click to close"
          />
        </div>
      )}
    </>
  );
}
